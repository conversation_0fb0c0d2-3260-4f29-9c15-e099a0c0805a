import type { CollectionConfig } from 'payload'

export const Members: CollectionConfig = {
  slug: 'members',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'position', 'category', 'updatedAt'],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'slug',
      type: 'text',
      admin: {
        position: 'sidebar',
      },
      hooks: {
        beforeValidate: [
          ({ data }) => {
            if (data?.name) {
              return data.name
                .toLowerCase()
                .replace(/ /g, '-')
                .replace(/[^\w-]+/g, '')
            }
          },
        ],
      },
    },
    {
      name: 'category',
      type: 'select',
      required: true,
      options: [
        {
          label: 'Faculty Members',
          value: 'faculty',
        },
        {
          label: 'Postdoctor and PhD Students',
          value: 'phd',
        },
        {
          label: 'Graduate Students',
          value: 'graduate',
        },
        {
          label: 'Undergraduate Students',
          value: 'undergraduate',
        },
        {
          label: 'Alumni/Alumnae',
          value: 'alumni',
        },
      ],
    },
    {
      name: 'position',
      type: 'text',
      required: true,
    },
    {
      name: 'mentor',
      type: 'text',
      admin: {
        condition: (data) => data.category !== 'faculty',
      },
    },
    {
      name: 'photo',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'personalWebsite',
      type: 'text',
      admin: {
        placeholder: 'https://example.com',
      },
    },
    {
      name: 'email',
      type: 'email',
    },
    {
      name: 'researchInterests',
      type: 'textarea',
    },
    {
      name: 'bio',
      type: 'richText',
    },
    {
      name: 'publications',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'authors',
          type: 'text',
          required: true,
        },
        {
          name: 'venue',
          type: 'text',
        },
        {
          name: 'year',
          type: 'number',
        },
        {
          name: 'url',
          type: 'text',
        },
      ],
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        position: 'sidebar',
      },
    },
  ],
}

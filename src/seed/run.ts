import { config } from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'
import seed from './index'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

// Load environment variables from the root directory
config({ path: path.resolve(dirname, '../../.env') })

// Ensure environment variables are set
if (!process.env.PAYLOAD_SECRET) {
  console.error('PAYLOAD_SECRET environment variable is required')
  process.exit(1)
}

if (!process.env.DATABASE_URI) {
  console.error('DATABASE_URI environment variable is required')
  process.exit(1)
}

console.log('Environment variables loaded:')
console.log('PAYLOAD_SECRET:', process.env.PAYLOAD_SECRET ? '***' : 'NOT SET')
console.log('DATABASE_URI:', process.env.DATABASE_URI)

const runSeed = async () => {
  try {
    await seed()
    process.exit(0)
  } catch (error) {
    console.error('Failed to seed database:', error)
    process.exit(1)
  }
}

runSeed()

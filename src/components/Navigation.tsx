'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState } from 'react'
import { Menu, X, Globe } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu'
import { cn } from '@/lib/utils'

const navigationItems = [
  { href: '/', label: 'About' },
  { href: '/research', label: 'Research' },
  { href: '/members', label: 'Members' },
  { href: '/events', label: 'Events' },
  { href: '/resources', label: 'Resources' },
  { href: 'http://bcmi.sjtu.edu.cn/bcmiforum', label: 'Forum', external: true },
]

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <Image
            src="/img/bcmi.logo.small.png"
            alt="BCMI Logo"
            width={40}
            height={40}
            className="h-8 w-auto"
          />
          <span className="hidden font-bold sm:inline-block">
            BCMI Laboratory
          </span>
        </Link>

        {/* Desktop Navigation */}
        <NavigationMenu className="hidden md:flex">
          <NavigationMenuList>
            {navigationItems.map((item) => (
              <NavigationMenuItem key={item.href}>
                {item.external ? (
                  <NavigationMenuLink
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={navigationMenuTriggerStyle()}
                  >
                    {item.label}
                  </NavigationMenuLink>
                ) : (
                  <Link href={item.href} legacyBehavior passHref>
                    <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                      {item.label}
                    </NavigationMenuLink>
                  </Link>
                )}
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        {/* Language Toggle & Mobile Menu */}
        <div className="flex items-center space-x-2">
          {/* Language Toggle */}
          <Button variant="ghost" size="sm" className="hidden sm:flex">
            <Globe className="h-4 w-4 mr-1" />
            English
          </Button>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden border-t bg-background">
          <nav className="container py-4">
            <div className="flex flex-col space-y-2">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "px-3 py-2 text-sm font-medium rounded-md hover:bg-accent hover:text-accent-foreground",
                    item.external && "flex items-center"
                  )}
                  target={item.external ? "_blank" : undefined}
                  rel={item.external ? "noopener noreferrer" : undefined}
                  onClick={() => setIsOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
              <Button variant="ghost" size="sm" className="justify-start sm:hidden">
                <Globe className="h-4 w-4 mr-2" />
                English / 中文
              </Button>
            </div>
          </nav>
        </div>
      )}
    </header>
  )
}
